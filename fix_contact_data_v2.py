import re
import json
import csv
from typing import Dict, List, Optional
from dataclasses import dataclass

@dataclass
class Contact:
    name: str = ""
    title: str = ""
    company: str = ""
    email: str = ""
    mobile_phone: str = ""
    direct_phone: str = ""
    hq_phone: str = ""
    location: str = ""
    
    def is_valid(self) -> bool:
        """Check if contact has minimum required information"""
        return bool(self.name and (self.email or self.mobile_phone))
    
    def to_dict(self) -> Dict:
        return {
            'Name': self.name,
            'Title': self.title,
            'Company': self.company,
            'Email': self.email,
            'Mobile Phone': self.mobile_phone,
            'Direct Phone': self.direct_phone,
            'HQ Phone': self.hq_phone,
            'Location': self.location
        }

def clean_and_extract_contacts(input_file: str) -> List[Contact]:
    """
    Clean OCR errors and extract valid contacts from the text file.
    """
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split content by page markers
    page_pattern = r'--- Page \d+ ---'
    pages = re.split(page_pattern, content)
    
    contacts = []
    
    for page_content in pages:
        if not page_content.strip():
            continue
            
        contact = extract_contact_from_page(page_content)
        if contact and contact.is_valid():
            contacts.append(contact)
    
    return contacts

def extract_contact_from_page(page_content: str) -> Optional[Contact]:
    """Extract and validate contact information from a single page."""

    lines = [line.strip() for line in page_content.split('\n') if line.strip()]

    if not lines:
        return None

    contact = Contact()
    name_found = False
    title_found = False

    # Extract name (first meaningful line that looks like a person's name)
    for i, line in enumerate(lines):
        if line and not any(skip in line.lower() for skip in ['main contact', 'additional contact', 'location', 'crm', 'export', 'tag', 'likely']):
            # Clean name
            name = clean_name(line)
            if name and len(name.split()) >= 2 and is_person_name(name):
                contact.name = name
                name_found = True

                # Look for title in next few lines
                for j in range(i+1, min(i+4, len(lines))):
                    if is_job_title(lines[j]):
                        contact.title = clean_title(lines[j])
                        title_found = True

                        # Look for company after title
                        for k in range(j+1, min(j+3, len(lines))):
                            company_candidate = clean_company_name(lines[k])
                            if is_likely_company(company_candidate) and company_candidate != contact.name:
                                contact.company = company_candidate
                                break
                        break
                break

    # If no company found yet, look for company patterns throughout the page
    if not contact.company:
        for line in lines:
            company_candidate = clean_company_name(line)
            if is_likely_company(company_candidate) and company_candidate != contact.name:
                contact.company = company_candidate
                break

    # Extract email
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    for line in lines:
        email_match = re.search(email_pattern, line)
        if email_match:
            contact.email = email_match.group()
            break

    # Extract phone numbers with better patterns
    for line in lines:
        # Mobile phone pattern
        mobile_match = re.search(r'\+971\s*\d{2}\s*\d{3}\s*\d{4}\s*\(M\)', line)
        if mobile_match:
            contact.mobile_phone = clean_phone_number(mobile_match.group())
            continue

        # Direct phone pattern
        direct_match = re.search(r'\+971\s*\d{1,2}\s*\d{3}\s*\d{4}\s*\(D\)', line)
        if direct_match:
            contact.direct_phone = clean_phone_number(direct_match.group())
            continue

        # HQ phone pattern
        hq_match = re.search(r'\+971\s*\d{8,9}\s*\(HQ\)', line)
        if hq_match:
            contact.hq_phone = clean_phone_number(hq_match.group())
            continue

    # Extract location
    location_lines = []
    in_location_section = False

    for line in lines:
        if line.lower().startswith('location') or line.lower().startswith('local'):
            in_location_section = True
            continue
        elif in_location_section:
            if any(section in line.lower() for section in ['crm', 'main contact', 'additional contact']):
                break
            if line and not line.startswith('---'):
                location_lines.append(line)

    if location_lines:
        contact.location = clean_location(' '.join(location_lines))

    return contact

def is_person_name(text: str) -> bool:
    """Check if text looks like a person's name."""
    if not text:
        return False

    words = text.split()
    if len(words) < 2:
        return False

    # Check if it contains job title keywords (then it's not a name)
    title_keywords = ['ceo', 'cto', 'cfo', 'director', 'manager', 'president', 'founder', 'vice', 'senior', 'head', 'lead']
    text_lower = text.lower()
    if any(keyword in text_lower for keyword in title_keywords):
        return False

    # Check if it contains company indicators
    company_indicators = ['properties', 'real estate', 'group', 'company', 'corp', 'inc', 'llc', 'ltd']
    if any(indicator in text_lower for indicator in company_indicators):
        return False

    # Check if words start with capital letters (typical for names)
    return all(word[0].isupper() for word in words if word)

def is_job_title(text: str) -> bool:
    """Check if text looks like a job title."""
    title_keywords = [
        'ceo', 'cto', 'cfo', 'coo', 'founder', 'co-founder', 'director', 'managing director',
        'executive director', 'senior director', 'vice president', 'vp', 'president',
        'manager', 'senior manager', 'head', 'lead', 'chairman', 'chairwoman'
    ]

    text_lower = text.lower()
    return any(keyword in text_lower for keyword in title_keywords)

def clean_name(name: str) -> str:
    """Clean and validate name."""
    # Remove common OCR artifacts
    name = re.sub(r'[^\w\s.-]', '', name)
    name = re.sub(r'\s+', ' ', name).strip()
    
    # Remove single characters at the beginning
    name = re.sub(r'^[a-z]\s+', '', name, flags=re.IGNORECASE)
    
    # Capitalize properly
    words = name.split()
    cleaned_words = []
    for word in words:
        if len(word) > 1:
            cleaned_words.append(word.capitalize())
    
    return ' '.join(cleaned_words)

def clean_title(title: str) -> str:
    """Clean job title."""
    # Remove common OCR artifacts
    title = re.sub(r'[^\w\s&.-]', '', title)
    title = re.sub(r'\s+', ' ', title).strip()
    
    # Fix common title abbreviations
    title_fixes = {
        'ceo': 'CEO',
        'cto': 'CTO',
        'cfo': 'CFO',
        'coo': 'COO',
        'vp': 'VP',
        'svp': 'SVP'
    }
    
    for abbrev, full in title_fixes.items():
        title = re.sub(r'\b' + abbrev + r'\b', full, title, flags=re.IGNORECASE)
    
    return title

def clean_company_name(company: str) -> str:
    """Clean company name."""
    # Remove common OCR artifacts and prefixes
    company = re.sub(r'^[@#$%^&*()_+=\[\]{}|\\:";\'<>?,./`~-]+', '', company)
    company = re.sub(r'[^\w\s&.-]', '', company)
    company = re.sub(r'\s+', ' ', company).strip()
    
    return company

def is_likely_company(text: str) -> bool:
    """Check if text is likely a company name."""
    if not text or len(text) < 3:
        return False

    # Skip if it looks like a title or contact info
    skip_patterns = [
        r'main contact', r'additional contact', r'location', r'crm',
        r'director', r'manager', r'president', r'founder', r'ceo',
        r'\+\d', r'@', r'\(', r'\)', r'likely', r'export', r'tag'
    ]

    text_lower = text.lower()
    if any(re.search(pattern, text_lower) for pattern in skip_patterns):
        return False

    # Skip if it's clearly a person's name (First Last format)
    words = text.split()
    if len(words) == 2 and all(word[0].isupper() and word[1:].islower() for word in words):
        return False

    # Strong company indicators
    strong_indicators = [
        'properties', 'real estate', 'group', 'company', 'corp', 'inc',
        'llc', 'ltd', 'development', 'investments', 'solutions',
        'technologies', 'services', 'international', 'global', 'brokers',
        'realty', 'developers', 'management', 'holdings', 'ventures',
        'capital', 'partners', 'associates', 'enterprises', 'consulting',
        'hub', 'center', 'centre', 'mall', 'tower', 'square', 'homes',
        'estate', 'land', 'property', 'investment', 'business', 'setup'
    ]

    # Check for strong indicators
    if any(indicator in text_lower for indicator in strong_indicators):
        return True

    # Check for specific known company patterns
    known_patterns = [
        r'\b\w+\s+(properties|realty|group|development|investments?)\b',
        r'\b(al|the|dubai|abu dhabi|emirates)\s+\w+',
        r'\b\w+\s+(real\s+estate|property)\b'
    ]

    if any(re.search(pattern, text_lower) for pattern in known_patterns):
        return True

    # If it's short and doesn't contain person name indicators, might be company
    if len(words) <= 3 and not all(word.istitle() for word in words):
        return True

    return False

def clean_phone_number(phone: str) -> str:
    """Clean and format phone number."""
    # Extract just the numbers and country code
    numbers = re.findall(r'\d+', phone)
    if numbers:
        return '+' + ''.join(numbers)
    return phone

def clean_location(location: str) -> str:
    """Clean location information."""
    # Remove common OCR artifacts
    location = re.sub(r'[^\w\s,.-]', '', location)
    location = re.sub(r'\s+', ' ', location).strip()
    
    # Fix common location issues
    location_fixes = {
        'dubai dubai': 'Dubai',
        'united arab emirates': 'United Arab Emirates',
        'uae': 'UAE'
    }
    
    location_lower = location.lower()
    for old, new in location_fixes.items():
        location_lower = location_lower.replace(old, new)
    
    return location_lower.title()

def save_contacts_to_csv(contacts: List[Contact], output_file: str) -> None:
    """Save contacts to CSV file."""
    
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['Name', 'Title', 'Company', 'Email', 'Mobile Phone', 'Direct Phone', 'HQ Phone', 'Location']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for contact in contacts:
            writer.writerow(contact.to_dict())
    
    print(f"✓ {len(contacts)} contacts saved to CSV: {output_file}")

def save_contacts_to_json(contacts: List[Contact], output_file: str) -> None:
    """Save contacts to JSON file."""
    
    contacts_data = [contact.to_dict() for contact in contacts]
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(contacts_data, f, indent=2, ensure_ascii=False)
    
    print(f"✓ {len(contacts)} contacts saved to JSON: {output_file}")

def validate_and_report(contacts: List[Contact]) -> None:
    """Validate contacts and provide detailed report."""
    
    total_contacts = len(contacts)
    contacts_with_email = sum(1 for c in contacts if c.email)
    contacts_with_phone = sum(1 for c in contacts if c.mobile_phone or c.direct_phone)
    contacts_with_company = sum(1 for c in contacts if c.company)
    contacts_with_location = sum(1 for c in contacts if c.location)
    
    print(f"\n📊 Validation Report:")
    print(f"   • Total valid contacts: {total_contacts}")
    print(f"   • Contacts with email: {contacts_with_email} ({contacts_with_email/total_contacts*100:.1f}%)")
    print(f"   • Contacts with phone: {contacts_with_phone} ({contacts_with_phone/total_contacts*100:.1f}%)")
    print(f"   • Contacts with company: {contacts_with_company} ({contacts_with_company/total_contacts*100:.1f}%)")
    print(f"   • Contacts with location: {contacts_with_location} ({contacts_with_location/total_contacts*100:.1f}%)")

def main():
    """Main function to process and validate contact data."""
    
    input_file = "output_text.txt"
    csv_output = "contacts_cleaned.csv"
    json_output = "contacts_cleaned.json"
    
    print("🔧 Processing contact data with validation...")
    
    # Extract and clean contacts
    contacts = clean_and_extract_contacts(input_file)
    
    if not contacts:
        print("❌ No valid contacts found!")
        return
    
    # Validate and report
    validate_and_report(contacts)
    
    # Save to files
    print(f"\n💾 Saving cleaned data...")
    save_contacts_to_csv(contacts, csv_output)
    save_contacts_to_json(contacts, json_output)
    
    # Show preview
    print(f"\n📋 Preview of first 5 contacts:")
    for i, contact in enumerate(contacts[:5], 1):
        print(f"   {i}. {contact.name}")
        print(f"      Title: {contact.title or 'N/A'}")
        print(f"      Company: {contact.company or 'N/A'}")
        print(f"      Email: {contact.email or 'N/A'}")
        print(f"      Mobile: {contact.mobile_phone or 'N/A'}")
        print(f"      Location: {contact.location or 'N/A'}")
        print()

if __name__ == "__main__":
    main()
