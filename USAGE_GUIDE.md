# Dynamic Contact Processor - Usage Guide

## 🚀 **Single File Solution**

The `dynamic_contact_processor.py` script processes **any ZIP or PDF file** and converts it directly to Excel format with contact information.

## 📋 **Features**
- ✅ **Dynamic processing** - Works with any ZIP or PDF file
- ✅ **No static data** - Processes whatever file you provide
- ✅ **No validation** - Null fields automatically set to "N/A"
- ✅ **Standard format** - Always outputs the same table structure
- ✅ **Single command** - One script does everything

## 📊 **Output Format**
```
Name | Title | Company | Email | Mobile Phone | Direct Phone | HQ Phone | Location
```

## 🔧 **Usage**

### Command Line Usage:
```bash
# Process ZIP file (auto-generates output name)
python dynamic_contact_processor.py contacts.zip

# Process PDF file with custom output name
python dynamic_contact_processor.py document.pdf my_contacts.xlsx

# Process any supported file
python dynamic_contact_processor.py "Screenshot 2025-07-07 171612.zip" output.xlsx
```

### Programmatic Usage:
```python
from dynamic_contact_processor import process_file

# Process file and get Excel output
excel_file = process_file("contacts.zip", "output.xlsx")
print(f"Contacts saved to: {excel_file}")
```

## 📁 **Supported Input Formats**
- **ZIP files** containing images (PNG, JPG, JPEG, BMP, TIFF, GIF)
- **PDF files** (text-based or scanned images)

## 📈 **Processing Steps**
1. **ZIP → PDF** (if input is ZIP)
2. **PDF → Text** (OCR extraction)
3. **Text → Contacts** (dynamic parsing)
4. **Contacts → Excel** (formatted output)

## 🎯 **Example Results**
```
📦 Processing ZIP file: contacts.zip
Step 1: Converting ZIP to PDF...
✓ PDF created: temp_contacts.pdf
Step 2: Extracting text from PDF...
✓ Text extracted: temp_contacts_text.txt
Step 3: Extracting contact information...
✓ Found 99 contacts
Step 4: Creating Excel file...
✓ Excel file created: contacts_contacts.xlsx

📊 Summary:
   • Input file: contacts.zip
   • Output Excel: contacts_contacts.xlsx
   • Contacts extracted: 99

✅ Processing complete!
```

## 🔧 **Requirements**
```bash
pip install pillow fpdf2 pytesseract pymupdf openpyxl pandas
```

## 📝 **Notes**
- **Tesseract OCR** must be installed for image text extraction
- **Temporary files** are automatically cleaned up
- **N/A values** are used for missing/null fields
- **No validation** - all extracted data is included as-is
- **Dynamic extraction** - adapts to different document formats

## 🎯 **Perfect For**
- Processing different contact document formats
- Batch processing multiple files
- Converting scanned documents to structured data
- Creating contact databases from various sources

## 📞 **Quick Test**
```bash
# Test with your existing file
python dynamic_contact_processor.py "Screenshot 2025-07-07 171612.zip"
```

This will create `Screenshot 2025-07-07 171612_contacts.xlsx` with all extracted contacts!
