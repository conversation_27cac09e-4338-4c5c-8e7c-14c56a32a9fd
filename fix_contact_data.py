import re
import json
from typing import Dict, List, Optional

def clean_text_data(input_file: str, output_file: str) -> None:
    """
    Clean and fix OCR errors in the contact data text file.

    Args:
        input_file: Path to the input text file with OCR errors
        output_file: Path to save the cleaned text file
    """

    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # Apply various cleaning rules
    cleaned_content = apply_cleaning_rules(content)

    # Write cleaned content to output file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(cleaned_content)

    print(f"✓ Cleaned data saved to: {output_file}")

def apply_cleaning_rules(content: str) -> str:
    """Apply comprehensive cleaning rules to fix OCR errors."""

    # 1. Fix common OCR character substitutions
    char_fixes = {
        'SeMERIEE': 'SERIES',
        'COPY mobile phone': 'mobile phone',
        'Ist FI': '1st Fl',
        'Bnbme': 'BnbMe',
        'G=ID': 'ID',
        'G=ED': 'ID',
        'CG=ED': 'ID',
        'G=ap': 'ID',
        'Gap': 'ID',
        'HBAaBO': '',
        'HBAeo': '',
        'Hao': '',
        'Bao': '',
        'Ho': '',
        'HO': '',
        'lin}': '',
        'Lin}': '',
        'Lin]': '',
        'fin]': '',
        'fino}': '',
        'io}': '',
        'ia}': '',
        'linia}': '',
        'lacation': 'Location',
        'ctor,': 'Director,',
        'Di': 'Director',
        'ional': 'ditional',
        'Ad': 'Additional',
        'Addi': 'Additional',
        '® Export': '',
        '® export': '',
        '© likelytoEngage': '',
        '© likelyto—ngage': '',
        '© Likely to Engage': '',
        '© LikelytoEngage': '',
        'likelytoEngage': '',
        'Tag': '',
        'lv)': '',
        '[v)': '',
        '(~)': '',
        '|~': '',
        '| ~': '',
        '(4': '',
        '(a)': '',
        '(a+)': '',
        '(s+)': '',
        '(s-J': '',
        '(+)': '',
        '(n]': '',
        '(x)': '',
        '()': '',
        'De stake': 'Stake',
        'Dp sultan': 'Sultan',
        'Be Attractive': 'Attractive',
        '@e': '',
        '@o': '',
        'Bg': '',
        'Bay': '',
        '2g': '',
        '% ': '',
        '», ': '',
        '» ': '',
        '> ': '',
        '~ ': '',
        '— ': '',
        '—~ ': '',
        '2 ': '',
        '7': '',
        'f ': '',
        r'\ ': '',
        'a ': '',
        '= ': '',
        'wy.)': '',
        'Me': '',
        'Ye': '',
        'ts)': '',
        'q «': '',
        'mp:': '',
        '=p': '',
        '=p:': '',
        '=p: :': '',
        'ff': '',
        'fi': '',
        'M': '',
        'MAB': '',
        'MM': '',
        'O': '',
        '&': '',
        '6': '',
        '(OD': '',
        '(0': '',
        'to export': '',
        'MAR': '',
        'HQ Finance Building St': 'Finance Building St',
        'Clock Tower Roundabout': 'Clock Tower Roundabout',
        'Al Mamzar Beach St M03': 'Al Mamzar Beach St',
        'Dubai Media': 'Dubai Media City',
        'Dubai International Financial Cetner': 'Dubai International Financial Center',
        'Silver Tower Business Baydubai': 'Silver Tower Business Bay Dubai',
        'Office 2705, Dubai, Dubai': 'Office 2705, Dubai',
        'Marasi Dr 1204': 'Marasi Drive 1204',
        'Festival Tower 19th Fl': 'Festival Tower 19th Floor',
        'Office 511 Building 03 Dubai Design District D3': 'Office 511 Building 03 Dubai Design District',
        'Al Marjan Marjan Iceland Blvd': 'Al Marjan Island Blvd',
        'Office 202 Bldg 6 Bay Sq': 'Office 202 Building 6 Bay Square',
        '2101 Business Bay': '2101 Business Bay',
        'United Kingdom.': 'United Kingdom'
    }

    # Apply character fixes
    for old, new in char_fixes.items():
        content = content.replace(old, new)

    # 2. Fix phone number formatting issues
    content = re.sub(r'\+(\d{3})(\d{2})\s+(\d{3})\s+(\d{4})', r'+\1 \2 \3 \4', content)
    content = re.sub(r'\+(\d{3})\s+(\d{1})\s+(\d{3})\s+(\d{4})', r'+\1 \2 \3 \4', content)
    content = re.sub(r'\+(\d{3})\s+(\d{2})\s+(\d{3})\s+(\d{4})', r'+\1 \2 \3 \4', content)

    # 3. Clean up email formatting
    content = re.sub(r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\s+\([B8]\)', r'\1 (B)', content)

    # 4. Fix truncated names and titles
    title_fixes = {
        'Co-Chief Executive Off...': 'Co-Chief Executive Officer',
        'Chief Product & Techn...': 'Chief Product & Technology Officer',
        'Marketing & Commun...': 'Marketing & Communications',
        'Sales Operations & Ag...': 'Sales Operations & Agency',
        'Real Estate Developm...': 'Real Estate Development',
        'Industrial Zone Busine...': 'Industrial Zone Business',
        'Managing Director and Co-founder': 'Managing Director & Co-Founder',
        'Director, Projects & Owner & Represe...': 'Director, Projects & Owner Representative',
        'Assistant VP, To Sales & Director, Sale': 'Assistant VP Sales & Director Sales',
        'Senior Head of Real Estate, Director Pr...': 'Senior Head of Real Estate, Director Projects',
        'Engineering & Develo...': 'Engineering & Development',
        'Executive Director - Valuation and Ad...': 'Executive Director - Valuation and Advisory',
        'Group Vice President, Industrial Busi': 'Group Vice President, Industrial Business',
        'Vice President, Sales At Range Intern...': 'Vice President, Sales at Range International',
        'D & B Properties - Chairman and Fou...': 'D & B Properties - Chairman and Founder',
        'Managing Director, Owner & Represe...': 'Managing Director, Owner & Representative',
        'Facilities Director, Operations': 'Facilities Director, Operations',
        'Vice President, Business Setup Servic...': 'Vice President, Business Setup Services',
        'Director, Human Resources & Operati...': 'Director, Human Resources & Operations',
        'Madhusudhan Ramachand...': 'Madhusudhan Ramachandran',
        'Hentry Chinnamma Abraha...': 'Henry Chinnamma Abraham'
    }

    for old, new in title_fixes.items():
        content = content.replace(old, new)

    # 5. Remove empty lines and clean up spacing
    lines = content.split('\n')
    cleaned_lines = []

    for line in lines:
        line = line.strip()
        # Skip empty lines and lines with only special characters
        if line and not re.match(r'^[^\w\s]*$', line):
            cleaned_lines.append(line)

    # 6. Remove duplicate consecutive lines
    final_lines = []
    prev_line = ""
    for line in cleaned_lines:
        if line != prev_line:
            final_lines.append(line)
        prev_line = line

    return '\n'.join(final_lines)

def extract_structured_contacts(cleaned_file: str) -> List[Dict]:
    """
    Extract structured contact information from cleaned text.

    Args:
        cleaned_file: Path to the cleaned text file

    Returns:
        List of contact dictionaries
    """

    with open(cleaned_file, 'r', encoding='utf-8') as f:
        content = f.read()

    contacts = []

    # Split content by page markers
    pages = re.split(r'--- Page \d+ ---', content)

    for page in pages:
        if not page.strip():
            continue

        contact = extract_contact_from_page(page.strip())
        if contact and contact.get('Name'):
            contacts.append(contact)

    return contacts

def extract_contact_from_page(page_content: str) -> Optional[Dict]:
    """Extract contact information from a single page."""

    lines = [line.strip() for line in page_content.split('\n') if line.strip()]

    if not lines:
        return None

    contact = {
        'Name': '',
        'Title': '',
        'Company': '',
        'Email': '',
        'Mobile Phone': '',
        'Direct Phone': '',
        'HQ Phone': '',
        'Location': ''
    }

    # Extract name (usually first line)
    if lines:
        contact['Name'] = lines[0]

    # Extract email
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    for line in lines:
        email_match = re.search(email_pattern, line)
        if email_match:
            contact['Email'] = email_match.group()
            break

    # Extract phone numbers
    mobile_pattern = r'\+\d{3}\s*\d{2}\s*\d{3}\s*\d{4}\s*\(M\)'
    direct_pattern = r'\+\d{3}\s*\d{1,2}\s*\d{3}\s*\d{4}\s*\(D\)'
    hq_pattern = r'\+\d{3}\s*\d{8,9}\s*\(HQ\)'

    for line in lines:
        if re.search(mobile_pattern, line):
            phone = re.search(r'\+\d{3}\s*\d{2}\s*\d{3}\s*\d{4}', line)
            if phone:
                contact['Mobile Phone'] = phone.group()
        elif re.search(direct_pattern, line):
            phone = re.search(r'\+\d{3}\s*\d{1,2}\s*\d{3}\s*\d{4}', line)
            if phone:
                contact['Direct Phone'] = phone.group()
        elif re.search(hq_pattern, line):
            phone = re.search(r'\+\d{3}\s*\d{8,9}', line)
            if phone:
                contact['HQ Phone'] = phone.group()

    # Extract title and company
    title_keywords = ['CEO', 'CTO', 'CFO', 'COO', 'Manager', 'Director', 'President', 'Vice President', 'VP', 'Senior', 'Lead', 'Founder', 'Chairman', 'Executive']

    for i, line in enumerate(lines[1:], 1):  # Skip name line
        # Check if line contains title keywords
        if any(keyword.lower() in line.lower() for keyword in title_keywords):
            contact['Title'] = line
            # Next line might be company
            if i + 1 < len(lines):
                next_line = lines[i + 1]
                if not any(keyword in next_line for keyword in ['Main Contact', 'Additional Contact', 'Location', 'CRM']):
                    contact['Company'] = next_line
            break

    # Extract location
    location_started = False
    location_parts = []

    for line in lines:
        if line.startswith('Location') or line.startswith('Local'):
            location_started = True
            continue
        elif location_started:
            if line.startswith('CRM') or line.startswith('Main Contact') or line.startswith('Additional Contact'):
                break
            location_parts.append(line)

    if location_parts:
        contact['Location'] = ' '.join(location_parts)

    return contact

def save_contacts_to_json(contacts: List[Dict], output_file: str) -> None:
    """Save contacts to JSON file for easy processing."""

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(contacts, f, indent=2, ensure_ascii=False)

    print(f"✓ {len(contacts)} contacts saved to: {output_file}")

def main():
    """Main function to clean and process contact data."""

    input_file = "output_text.txt"
    cleaned_file = "output_text_cleaned.txt"
    contacts_json = "contacts_cleaned.json"

    print("Step 1: Cleaning OCR errors and formatting issues...")
    clean_text_data(input_file, cleaned_file)

    print("\nStep 2: Extracting structured contact information...")
    contacts = extract_structured_contacts(cleaned_file)

    print("\nStep 3: Saving contacts to JSON...")
    save_contacts_to_json(contacts, contacts_json)

    print(f"\n📊 Summary:")
    print(f"   • Original file: {input_file}")
    print(f"   • Cleaned file: {cleaned_file}")
    print(f"   • Contacts JSON: {contacts_json}")
    print(f"   • Contacts extracted: {len(contacts)}")

    # Show first few contacts as preview
    if contacts:
        print(f"\n📋 Preview of first 3 contacts:")
        for i, contact in enumerate(contacts[:3], 1):
            print(f"   {i}. {contact.get('Name', 'N/A')} - {contact.get('Title', 'N/A')}")
            print(f"      Company: {contact.get('Company', 'N/A')}")
            print(f"      Email: {contact.get('Email', 'N/A')}")
            print(f"      Mobile: {contact.get('Mobile Phone', 'N/A')}")
            print()

if __name__ == "__main__":
    main()