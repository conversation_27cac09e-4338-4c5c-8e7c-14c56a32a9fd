import re
import json
import csv
from typing import Dict, List, Optional
from dataclasses import dataclass

@dataclass
class Contact:
    name: str = ""
    title: str = ""
    company: str = ""
    email: str = ""
    mobile_phone: str = ""
    direct_phone: str = ""
    hq_phone: str = ""
    location: str = ""
    
    def is_valid(self) -> bool:
        """Check if contact has minimum required information"""
        return bool(self.name and (self.email or self.mobile_phone))
    
    def to_dict(self) -> Dict:
        return {
            'Name': self.name,
            'Title': self.title,
            'Company': self.company,
            'Email': self.email,
            'Mobile Phone': self.mobile_phone,
            'Direct Phone': self.direct_phone,
            'HQ Phone': self.hq_phone,
            'Location': self.location
        }

def clean_and_extract_contacts(input_file: str) -> List[Contact]:
    """Clean OCR errors and extract valid contacts from the text file."""
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split content by page markers
    page_pattern = r'--- Page \d+ ---'
    pages = re.split(page_pattern, content)
    
    contacts = []
    
    for page_content in pages:
        if not page_content.strip():
            continue
            
        contact = extract_contact_from_page(page_content)
        if contact and contact.is_valid():
            contacts.append(contact)
    
    return contacts

def extract_contact_from_page(page_content: str) -> Optional[Contact]:
    """Extract and validate contact information from a single page."""
    
    lines = [line.strip() for line in page_content.split('\n') if line.strip()]
    
    if not lines:
        return None
    
    contact = Contact()
    
    # Extract name (first line that looks like a person's name)
    for line in lines:
        cleaned_line = clean_text(line)
        if is_person_name(cleaned_line):
            contact.name = cleaned_line
            break
    
    # Extract title
    title_keywords = [
        'ceo', 'cto', 'cfo', 'coo', 'founder', 'co-founder', 'director', 'managing director',
        'executive director', 'senior director', 'vice president', 'vp', 'president',
        'manager', 'senior manager', 'head', 'lead', 'chairman', 'chairwoman'
    ]
    
    for line in lines:
        line_lower = line.lower()
        if any(keyword in line_lower for keyword in title_keywords):
            contact.title = clean_text(line)
            break
    
    # Extract company using known patterns and manual mapping
    contact.company = extract_company_name(lines)
    
    # Extract email
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    for line in lines:
        email_match = re.search(email_pattern, line)
        if email_match:
            contact.email = email_match.group()
            break
    
    # Extract phone numbers
    for line in lines:
        # Mobile phone
        mobile_match = re.search(r'\+971\s*\d{2}\s*\d{3}\s*\d{4}\s*\(M\)', line)
        if mobile_match:
            contact.mobile_phone = clean_phone_number(mobile_match.group())
            
        # Direct phone
        direct_match = re.search(r'\+971\s*\d{1,2}\s*\d{3}\s*\d{4}\s*\(D\)', line)
        if direct_match:
            contact.direct_phone = clean_phone_number(direct_match.group())
            
        # HQ phone
        hq_match = re.search(r'\+971\s*\d{8,9}\s*\(HQ\)', line)
        if hq_match:
            contact.hq_phone = clean_phone_number(hq_match.group())
    
    # Extract location
    location_lines = []
    in_location_section = False
    
    for line in lines:
        if line.lower().startswith('location') or line.lower().startswith('local'):
            in_location_section = True
            continue
        elif in_location_section:
            if any(section in line.lower() for section in ['crm', 'main contact', 'additional contact']):
                break
            if line and not line.startswith('---'):
                location_lines.append(line)
    
    if location_lines:
        contact.location = clean_location(' '.join(location_lines))
    
    return contact

def extract_company_name(lines: List[str]) -> str:
    """Extract company name using patterns and known mappings."""
    
    # Known company mappings from the data
    company_mappings = {
        'stake': 'Stake',
        'bnbme': 'BnbMe',
        'union square house real estate': 'Union Square House Real Estate',
        'sky links real estate': 'Sky Links Real Estate',
        'gulf land property developers': 'Gulf Land Property Developers',
        'the appraisal hub': 'The Appraisal Hub',
        'rare homes real estate': 'Rare Homes Real Estate',
        'eva real estate': 'Eva Real Estate',
        'astro properties': 'Astro Properties',
        'luxhabitat': 'Luxhabitat',
        'bayut': 'Bayut',
        'estative': 'Estative',
        'octa properties': 'OCTA Properties',
        'gi properties': 'Gi Properties',
        'positive properties': 'Positive Properties',
        'attractive home': 'Attractive Home',
        'modus workspace': 'Modus Workspace',
        'one broker group': 'One Broker Group',
        'marjan': 'Marjan',
        'galadari brothers group': 'Galadari Brothers Group',
        'd&b properties': 'D&B Properties',
        'masakien al amna real estate': 'Masakien Al Amna Real Estate',
        'range international property investments': 'Range International Property Investments',
        'vincitore realty': 'Vincitore Realty',
        'arada': 'Arada',
        'baniyas investment and development': 'Baniyas Investment and Development',
        'rak properties': 'RAK Properties',
        'john buck international properties': 'John Buck International Properties',
        'flyingcolour business setup services': 'Flyingcolour Business Setup Services Dubai UAE',
        'kizad': 'KIZAD',
        'tiger group': 'Tiger Group',
        'al ain realtor': 'Al Ain Realtor',
        '3s real estate brokers': '3S Real Estate Brokers',
        'cavendish maxwell': 'Cavendish Maxwell',
        'sahara centre': 'Sahara Centre',
        'lead real estate developer': 'LEAD - Real Estate Developer',
        'mg properties': 'MG Properties',
        'knight frank uae': 'Knight Frank UAE',
        'trinity partners': 'Trinity Partners',
        'elysian real estate broker': 'Elysian Real Estate Broker',
        'wr properties': 'WR Properties',
        'real choice real estate brokers': 'Real Choice Real Estate Brokers',
        'the cayan group': 'The Cayan Group',
        'asteco property management': 'Asteco Property Management',
        'h&h development': 'H&H Development',
        'deja vu real estate': 'Deja Vu Real Estate',
        'espace real estate': 'Espace Real Estate',
        'expert homes real estate': 'Expert Homes Real Estate',
        'range developments': 'Range Developments',
        'alich real estate broker': 'Alich Real Estate Broker',
        'the heart of europe': 'The Heart of Europe',
        'gulf sothebys international realty': "Gulf Sotheby's International Realty",
        'reem mall': 'Reem Mall',
        'unique properties': 'Unique Properties',
        'reportage properties': 'Reportage Properties',
        'al majid property': 'Al Majid Property',
        'empire one': 'Empire One',
        'ohana development': 'Ohana Development',
        'matthews southwest': 'Matthews Southwest',
        'core real estate': 'CORE Real Estate',
        'palladian properties': 'Palladian Properties'
    }
    
    # Look for company patterns in the lines
    for line in lines:
        line_clean = clean_text(line).lower()
        
        # Check against known mappings
        for key, company in company_mappings.items():
            if key in line_clean:
                return company
        
        # Check for company indicators
        company_indicators = [
            'properties', 'real estate', 'group', 'company', 'corp', 'inc',
            'llc', 'ltd', 'development', 'investments', 'solutions',
            'technologies', 'services', 'international', 'global', 'brokers',
            'realty', 'developers', 'management', 'holdings', 'ventures'
        ]
        
        if any(indicator in line_clean for indicator in company_indicators):
            # Clean and return the company name
            cleaned = clean_text(line)
            if len(cleaned) > 3 and not is_person_name(cleaned):
                return cleaned
    
    return ""

def is_person_name(text: str) -> bool:
    """Check if text looks like a person's name."""
    if not text:
        return False
    
    words = text.split()
    if len(words) < 2:
        return False
    
    # Skip if contains job title keywords
    title_keywords = ['ceo', 'cto', 'cfo', 'director', 'manager', 'president', 'founder', 'vice', 'senior']
    text_lower = text.lower()
    if any(keyword in text_lower for keyword in title_keywords):
        return False
    
    # Skip if contains company indicators
    company_indicators = ['properties', 'real estate', 'group', 'company', 'corp', 'inc', 'llc', 'ltd']
    if any(indicator in text_lower for indicator in company_indicators):
        return False
    
    # Check if it looks like a name (capitalized words)
    return all(word[0].isupper() for word in words if word and word.isalpha())

def clean_text(text: str) -> str:
    """Clean text from OCR artifacts."""
    if not text:
        return ""
    
    # Remove common OCR artifacts
    artifacts = [
        r'[@#$%^&*()_+=\[\]{}|\\:";\'<>?,./`~]',
        r'\b[a-z]\s+(?=[A-Z])',  # Single lowercase letters before capitals
        r'\s+', r'^\d+\s*',  # Multiple spaces, leading numbers
    ]
    
    for pattern in artifacts:
        text = re.sub(pattern, ' ', text)
    
    return text.strip()

def clean_phone_number(phone: str) -> str:
    """Clean and format phone number."""
    numbers = re.findall(r'\d+', phone)
    if numbers:
        return '+' + ''.join(numbers)
    return phone

def clean_location(location: str) -> str:
    """Clean location information."""
    location = re.sub(r'[^\w\s,.-]', '', location)
    location = re.sub(r'\s+', ' ', location).strip()
    
    # Fix common location issues
    location_fixes = {
        'dubai dubai': 'Dubai',
        'united arab emirates': 'United Arab Emirates',
        'uae': 'UAE'
    }
    
    location_lower = location.lower()
    for old, new in location_fixes.items():
        location_lower = location_lower.replace(old, new)
    
    return location_lower.title()

def save_contacts_to_csv(contacts: List[Contact], output_file: str) -> None:
    """Save contacts to CSV file."""
    
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['Name', 'Title', 'Company', 'Email', 'Mobile Phone', 'Direct Phone', 'HQ Phone', 'Location']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for contact in contacts:
            writer.writerow(contact.to_dict())
    
    print(f"✓ {len(contacts)} contacts saved to CSV: {output_file}")

def save_contacts_to_json(contacts: List[Contact], output_file: str) -> None:
    """Save contacts to JSON file."""
    
    contacts_data = [contact.to_dict() for contact in contacts]
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(contacts_data, f, indent=2, ensure_ascii=False)
    
    print(f"✓ {len(contacts)} contacts saved to JSON: {output_file}")

def validate_and_report(contacts: List[Contact]) -> None:
    """Validate contacts and provide detailed report."""
    
    total_contacts = len(contacts)
    contacts_with_email = sum(1 for c in contacts if c.email)
    contacts_with_phone = sum(1 for c in contacts if c.mobile_phone or c.direct_phone)
    contacts_with_company = sum(1 for c in contacts if c.company)
    contacts_with_location = sum(1 for c in contacts if c.location)
    
    print(f"\n📊 Validation Report:")
    print(f"   • Total valid contacts: {total_contacts}")
    print(f"   • Contacts with email: {contacts_with_email} ({contacts_with_email/total_contacts*100:.1f}%)")
    print(f"   • Contacts with phone: {contacts_with_phone} ({contacts_with_phone/total_contacts*100:.1f}%)")
    print(f"   • Contacts with company: {contacts_with_company} ({contacts_with_company/total_contacts*100:.1f}%)")
    print(f"   • Contacts with location: {contacts_with_location} ({contacts_with_location/total_contacts*100:.1f}%)")

def main():
    """Main function to process and validate contact data."""
    
    input_file = "output_text.txt"
    csv_output = "contacts_final.csv"
    json_output = "contacts_final.json"
    
    print("🔧 Processing contact data with comprehensive validation...")
    
    # Extract and clean contacts
    contacts = clean_and_extract_contacts(input_file)
    
    if not contacts:
        print("❌ No valid contacts found!")
        return
    
    # Validate and report
    validate_and_report(contacts)
    
    # Save to files
    print(f"\n💾 Saving cleaned and validated data...")
    save_contacts_to_csv(contacts, csv_output)
    save_contacts_to_json(contacts, json_output)
    
    # Show preview
    print(f"\n📋 Preview of first 5 contacts:")
    for i, contact in enumerate(contacts[:5], 1):
        print(f"   {i}. {contact.name}")
        print(f"      Title: {contact.title or 'N/A'}")
        print(f"      Company: {contact.company or 'N/A'}")
        print(f"      Email: {contact.email or 'N/A'}")
        print(f"      Mobile: {contact.mobile_phone or 'N/A'}")
        print(f"      Location: {contact.location or 'N/A'}")
        print()
    
    print(f"✅ Processing complete! Check {csv_output} and {json_output} for the cleaned data.")

if __name__ == "__main__":
    main()
